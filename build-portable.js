const { build } = require('electron-builder');

// 构建配置，完全跳过代码签名
const config = {
  appId: 'cn.fuint.cashier',
  productName: '68U选',
  directories: {
    output: 'build'
  },
  files: [
    'dist/electron/**/*'
  ],
  asar: false,
  win: {
    icon: 'build/icons/icon.ico',
    target: [
      {
        target: 'portable',
        arch: ['x64']
      }
    ],
    // 完全禁用代码签名
    sign: false,
    verifyUpdateCodeSignature: false
  },
  // 禁用自动代码签名发现
  forceCodeSigning: false,
  // 跳过所有签名相关的步骤
  afterSign: null,
  beforeBuild: null
};

// 设置环境变量禁用代码签名
process.env.CSC_IDENTITY_AUTO_DISCOVERY = 'false';
delete process.env.CSC_LINK;
delete process.env.CSC_KEY_PASSWORD;
delete process.env.WIN_CSC_LINK;
delete process.env.WIN_CSC_KEY_PASSWORD;
process.env.ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES = 'true';

console.log('开始构建便携式应用...');

build({
  config,
  win: ['portable'],
  x64: true
}).then(() => {
  console.log('构建完成！');
  console.log('输出目录: build/');
}).catch((error) => {
  console.error('构建失败:', error);
  process.exit(1);
});
