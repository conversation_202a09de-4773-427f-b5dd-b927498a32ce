# 以管理员权限运行的构建脚本
# 检查是否以管理员权限运行
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator"))
{
    Write-Host "需要管理员权限来运行此脚本。正在重新启动..." -ForegroundColor Red
    Start-Process PowerShell -Verb RunAs "-NoProfile -ExecutionPolicy Bypass -Command `"cd '$pwd'; & '$PSCommandPath'`""
    exit
}

Write-Host "正在以管理员权限构建应用..." -ForegroundColor Green

# 设置环境变量
$env:CSC_IDENTITY_AUTO_DISCOVERY = "false"
$env:ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES = "true"

# 清理缓存
Write-Host "清理electron-builder缓存..." -ForegroundColor Yellow
$cacheDir = "$env:LOCALAPPDATA\electron-builder\Cache"
if (Test-Path $cacheDir) {
    Remove-Item -Path $cacheDir -Recurse -Force -ErrorAction SilentlyContinue
    Write-Host "缓存清理完成" -ForegroundColor Green
}

# 运行构建
Write-Host "开始构建..." -ForegroundColor Yellow
try {
    npm run build:portable
    Write-Host "构建成功完成！" -ForegroundColor Green
    Write-Host "输出文件位于 build/ 目录" -ForegroundColor Cyan
} catch {
    Write-Host "构建失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "按任意键退出..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
