# Electron-Vue-template

> This is a project based on electron-vue, using the elementUI, vuex, vue-router, axios technology stack. This project is divided into two branches. The master maintains the original project structure and features for dependency updates, and the SynchronizedUpdates branch. It keeps the original file structure and the code part is completely customized by me personally; it includes: a complete background management interface, nedb database package, and two complete update download methods. If you feel redundant, you can delete it yourself.

[中文在线文档](https://umbrella22.github.io/electron-vue-template-doc/)
[国内访问地址](https://gitee.com/Zh-Sky/electron-vue-template)
[国内文档访问地址](https://zh-sky.gitee.io/electron-vue-template-doc/)
[vite 版本](https://github.com/umbrella22/electron-vite-template)
[vite 版本（码云）](https://gitee.com/Zh-Sky/electron-vite-template)

[Open in Visual Studio Code](https://open.vscode.dev/umbrella22/electron-vue-template)
![GitHub Repo stars](https://img.shields.io/github/stars/umbrella22/electron-vue-template)
[![vue](https://img.shields.io/badge/vue-2.7.10-brightgreen.svg)](https://github.com/vuejs/vue)
[![element-ui](https://img.shields.io/badge/element--ui-2.15.9-brightgreen.svg)](https://github.com/ElemeFE/element)
[![electron](https://img.shields.io/badge/electron-19.0.17-brightgreen.svg)](https://github.com/electron/electron)
[![license](https://img.shields.io/github/license/mashape/apistatus.svg)](https://github.com/umbrella22/electron-vue-template/blob/master/LICENSE)

- electron of react [Electron-react-template](https://github.com/umbrella22/electron-react-template)

### To run a project, you need to have **node version 16** or higher and **use yarn as your dependency management tool**

<p align="center">
  <a href="https://github.com/umbrella22/electron-vue-template">
    <img src="https://github.com/umbrella22/electron-vue-template/actions/workflows/build-test.yml/badge.svg">
  </a>
</p>

<h3 align="center">Thanks for support.</h3>

<p align="center">
  <a href="https://www.jetbrains.com/?from=electron-vue-template" target="_blank">
    <img width="160px" src="https://github.com/umbrella22/MCsever/blob/master/jetbrains.png">
  </a>
</p>

#### Build Setup

```bash
# install dependencies
yarn or yarn install

# serve with hot reload at localhost:9080
yarn dev

# build electron application for production
yarn build


```

---

# Function list

- Auto update
- Incremental update
- Loading animation before startup
- i18n
- Incremental update (wait for test)

# Built-in

- [vue-router](https://router.vuejs.org)
- [vuex](https://vuex.vuejs.org)
- [electron](http://www.electronjs.org/docs)
- electron-updater
- typescript
- [element-plus](https://element.eleme.cn/#/en-US)

# Note

- [gitee](https://gitee.com/Zh-Sky/electron-vue-template) is only for domestic users to pull code，from github to synchronize，please visit github for PR
- **Welcome to Issues and PR**

---

This project was generated with [electron-vue](https://github.com/SimulatedGREG/electron-vue)@[8fae476](https://github.com/SimulatedGREG/electron-vue/tree/8fae4763e9d225d3691b627e83b9e09b56f6c935) using [vue-cli](https://github.com/vuejs/vue-cli). Documentation about the original structure can be found [here](https://simulatedgreg.gitbooks.io/electron-vue/content/index.html).
Manage interface code address [here](https://github.com/PanJiaChen/electron-vue-admin)

# [CHANGELOG](CHANGELOG.md)
