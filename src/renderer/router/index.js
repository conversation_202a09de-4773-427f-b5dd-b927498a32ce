import Router from 'vue-router'
import Layout from '@/layout'
// 引入路由表
import asyncRouterMap from './constantRouterMap'

export const constantRouterMap = [{
  path: '/',
  component: Layout,
  redirect: '/dashboard',
  name: '主页',
  hidden: true,
  children: [{
    path: 'dashboard',
    name: '总览',
    component: () => import('@/views/home')
  }]
}, {
  path: '/login',
  component: () => import('@/views/login'),
  hidden: true
}, {
  path: '/cashier',
  component: () => import('@/views/cashier'),
  hidden: true
} , {
  path: '/setting',
  component: () => import('@/views/setting'),
  hidden: true
}, {
  path: '/serial-test',
  component: () => import('@/views/test/SerialPortTest'),
  hidden: true
}]
export const asyncRoutes = asyncRouterMap

const createRouter = () => new Router({
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
}
const router = createRouter()

export default router
