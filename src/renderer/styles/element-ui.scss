//to reset element-ui default css
 .el-upload {
   input[type="file"] {
     display: none !important;
   }
 }

 .el-upload__input {
   display: none;
 }

 //暂时性解决diolag 问题 https://github.com/ElemeFE/element/issues/2461
 .el-dialog {
   transform: none;
   left: 0;
   position: relative;
   margin: 0 auto;
 }

 //element ui upload
 .upload-container {
   .el-upload {
     width: 100%;

     .el-upload-dragger {
       width: 100%;
       height: 200px;
     }
   }
 }

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;
    .el-tag {
      margin-right: 0px;
    }
  }
}

 // common dialog
.common-dialog {
  .el-dialog {
    border: solid 0px #00acac;
  }
  .el-dialog__header{
    background: #00acac;
    padding: 10px;
  }
  .el-dialog__headerbtn {
    top: 15px;
  }
  .el-dialog__body {
    padding: 30px;
  }
  .el-dialog__title,.el-dialog__close{
    color: #fff;
    font-size: 14px;
  }
  .el-dialog__title,.el-dialog__close:hover{
    color: #fff;
    font-weight: bold;
  }
}
// main button
.main-button {
   background: #00acac;
   line-height: 40px;
   padding: 0px 20px 0px 20px;
   height: 40px;
}
.main-button-mini {
  background: #00acac;
  padding: 5px 8px 5px 8px;
}
.main-button-middle {
  background: #00acac;
  padding: 8px 12px 8px 12px;
}
.main-button:hover {
   background: #077171;
}
.main-button-mini:hover {
  background: #077171;
}
.main-button-middle:hover {
  background: #077171;
}
.main-button-reset {
  background: #ffffff;
}
.main-button-reset:hover {
  background: #f5f5f5;
  color: #333333;
}
.do-button {
  background: #ff5b57;
  line-height: 40px;
  padding: 0px 20px 0px 20px;
  height: 40px;
  border: solid 1px #cccccc;
}
.do-button:hover {
  background: #ff5b33;
  border: solid 1px #ff5b57;
}


