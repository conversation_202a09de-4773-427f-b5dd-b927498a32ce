<template>
  <el-dialog class="common-dialog" title="订单打印预览" :visible="showDialog" width="380px" @close="cancel" append-to-body destroy-on-close>
    <div v-if="orderInfo.id" class="print-area" id="printArea">
      <div class="base-info">
        <div class="name" v-if="storeInfo">{{ storeInfo.name }}</div>
        <div>**************************************************</div>
        <div class="no">订单编号：{{ orderInfo.orderSn }}</div>
        <div class="no" v-if="storeInfo.phone">联系电话：{{ storeInfo.phone ? storeInfo.phone : '无' }}</div>
        <div class="no" v-if="storeInfo.address">门店地址：{{ storeInfo.address ? storeInfo.address : '无' }}</div>
        <div class="no" v-if="orderInfo.payType">
          <span class="t">支付方式：</span>
          <span v-if="orderInfo.payType == 'JSAPI'">微信支付</span>
          <span v-if="orderInfo.payType == 'ALIPAY'">支付宝支付</span>
          <span v-if="orderInfo.payType == 'CASH'">现金支付</span>
          <span v-if="orderInfo.payType == 'BALANCE'">余额支付</span>
          <span v-if="orderInfo.payType == 'STORE'">门店支付</span>
          <span v-if="orderInfo.payType == 'MICROPAY'">微信扫码支付</span>
        </div>
        <div class="no">打印时间：{{formatDateTime(new Date())}}</div>
      </div>
      <div>**************************************************</div>
      <div class="goods-list" v-if="orderInfo.goods.length > 0">
        <div class="goods-header">
          <span class="item">商品</span>
          <span class="item" style="margin-left: 40px;">数量</span>
          <span class="item" style="margin-left: 40px;">单价</span>
          <span class="item" style="margin-left: 40px;">小计</span>
        </div>
        <div class="goods-item" v-for="(goodsInfo, index) in orderInfo.goods">
          <span class="item" style="font-size: 12px;">{{ index+1 }}.{{ goodsInfo.name }}</span>
          <div class="goods-info" >
            <span class="item" style="margin-left: 15%;font-size: 12px;">x{{ goodsInfo.num }}</span>
            <span class="item" style="margin-left: 18%;font-size: 12px;">￥{{ goodsInfo.price }}</span>
            <span class="item" style="margin-left: 18%;font-size: 12px;">￥{{ (goodsInfo.price * goodsInfo.num).toFixed(2) }}</span>
          </div>
        </div>
      </div>
      <!-- <div v-if="orderInfo.goods.length > 0">**************************************************</div> -->
      <div class="member-info" v-if="orderInfo.isVisitor == 'N'">
        <div>**************************************************</div>
        <div class="item" v-if="orderInfo.isVisitor == 'N'"><span class="t">会员名称：</span>{{ orderInfo.userInfo.name }}</div>
        <div class="item" v-if="orderInfo.isVisitor == 'N'"><span class="t">会员号码：</span>{{ orderInfo.userInfo.userNo ? orderInfo.userInfo.userNo : '-' }}</div>
        <div class="item" v-if="orderInfo.isVisitor == 'Y'"><span class="t">会员信息：</span>无</div>
      </div>
      <div v-if="orderInfo.orderMode == 'express' && orderInfo.address">**************************************************</div>
      <div class="address-info" v-if="orderInfo.orderMode == 'express' && orderInfo.address">
        <div class="item">收货人名：{{ orderInfo.address.name ? orderInfo.address.name : '-' }}</div>
        <div class="item">联系电话：{{ orderInfo.address.mobile ? orderInfo.address.mobile : '无' }}</div>
        <div class="item">详细地址：{{orderInfo.address.provinceName}}{{orderInfo.address.cityName}}{{orderInfo.address.regionName}}{{orderInfo.address.detail}}</div>
      </div>
      <div>**************************************************</div>
      <div class="total-info">
        <!--              <div class="item">订单类型：{{ orderInfo.typeName }}</div>-->
        <div class="item">订单时间：{{ orderInfo.createTime }}:00</div>
        <div class="item">优惠金额：<span class="discount">￥{{ orderInfo.discount.toFixed(2) }}</span></div>
        <div class="item discount-detail" v-if="getDiscountDetail()">{{ getDiscountDetail() }}</div>
        <div class="item">支付金额：<span class="amount">￥{{ orderInfo.payAmount.toFixed(2) }}</span></div>
      </div>
      <div>**************************************************</div>
      <div class="welcome">请保留好小票，作退换货凭证</div>
      <div class="welcome">谢谢惠顾，欢迎再次光临</div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" class="main-button" @click="handlePrint(printObj)" v-print="printObj" style="background: #00afff;border: 1px solid #00afff;">打印</el-button>
      <el-button @click="cancel()">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    showDialog: {
      type:[Boolean],
      default:()=>false
    },
    orderInfo: {
      type:[Object],
      default:()=>{}
    },
    storeInfo: {
      type:[Object],
      default:()=>{}
    }
  },
  data(){
    return {
      printObj: {
        id: "printArea",
        popTitle: '订单明细',
        extraCss: '',
        preview: false,
        previewTitle: '预览的标题',
        previewPrintBtnLabel: '预览结束，开始打印',
        extraHead: '',
        standard: 'loose'
      }
    }
  },
  methods: {
    handlePrint() {
      this.$emit('closeDialog','printOrder');
    },
    cancel() {
      this.$emit('closeDialog','printOrder');
    },
    formatDateTime(date) {
      const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false // 使用24小时制
      };
      return date.toLocaleString('zh-CN', options).replace(/\//g, '.');
    },
    // 获取优惠详情
    getDiscountDetail() {
      if (!this.orderInfo || !this.orderInfo.discount || this.orderInfo.discount <= 0) {
        return '';
      }

      const details = [];

      // 检查立减金额
      if (this.orderInfo.reduceAmount && this.orderInfo.reduceAmount > 0) {
        details.push(`立减${this.orderInfo.reduceAmount.toFixed(2)}元`);
      }

      // 检查优惠券抵扣
      if (this.orderInfo.couponInfo && this.orderInfo.couponInfo.name) {
        const couponAmount = this.orderInfo.couponInfo.amount || 0;
        if (couponAmount > 0) {
          details.push(`${couponAmount.toFixed(2)}元优惠券`);
        } else {
          details.push('优惠券');
        }
      }

      // 检查积分抵扣
      if (this.orderInfo.pointAmount && this.orderInfo.pointAmount > 0) {
        details.push(`积分抵扣${this.orderInfo.pointAmount.toFixed(2)}元`);
      }

      // 检查现金折扣（仅现金支付时显示）
      if (this.orderInfo.payType === 'CASH') {
        // 计算现金折扣金额
        const totalDiscount = this.orderInfo.discount || 0;
        const reduceAmount = this.orderInfo.reduceAmount || 0;
        const couponAmount = (this.orderInfo.couponInfo && this.orderInfo.couponInfo.amount) || 0;
        const pointAmount = this.orderInfo.pointAmount || 0;
        const cashDiscountAmount = totalDiscount - reduceAmount - couponAmount - pointAmount;

        if (cashDiscountAmount > 0) {
          details.push('现金折扣');
        }
      }

      if (details.length === 0) {
        return '';
      }

      return '(' + details.join('+') + ')';
    }
  }
}
</script>
<style scoped lang="scss">
.print-area {
  font-size: 14px;
  border: solid 1px #ccc;
  padding: 30px 10px 30px 10px;
  overflow: auto;
  width: 100%;
  .base-info {
    margin-bottom: 10px;
    .name {
      font-weight: bold;
      margin-bottom: 8px;
      margin-top: -10px;
      text-align: center;
    }
  }
  .goods-list {
    margin-top: 10px;
    margin-bottom: 15px;
    .goods-item {
      margin-bottom: 10px;
    }
    .goods-info {
      display: flex;
      margin-left: 60px;
    }
  }
  .member-info {
    margin-top: 10px;
    margin-bottom: 20px;
    .item {
      clear: both;
    }
  }
  .address-info {
    margin-top: 10px;
    margin-bottom: 20px;
  }
  .total-info {
    .item {
      margin-bottom: 2px;
      .amount {
        font-weight: bold;
        font-size: 15px;
      }
    }
    .discount-detail {
      font-size: 12px;
      color: #666;
      margin-left: 20px;
    }
  }
  .welcome {
    margin-top: 5px;
    text-align: center;
    font-size: 15px;
  }
}
.goods-header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px dashed #000;
  margin-bottom: 10px;
  padding-bottom: 10px;

  .item {
    flex: 1;
    text-align: center; // 使内容居中对齐
  }
}

</style>
<style media="print" lang="scss">
@page {
  size: auto;
  margin: 0mm;
}
@media print {
  html {
    height: auto;
    width: auto;
    margin: 0px;
  }
  body {
    border: solid 1px #ffffff;
  }
  #printArea {
    font-size: 14px;
    display: block;
    min-width: 320px;
    min-height: 420px;
    margin-top: 60px;
    .base-info {
      margin-bottom: 10px;
      .name {
        font-weight: bold;
        margin-bottom: 5px;
      }
    }
    .goods-list {
      margin-bottom: 20px;
      .goods-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        .item {
          fix:1;
          text-align: center;
          //margin-right: 10px;
          //margin-bottom: 10px;
        }
      }
    }
    .member-info {
      margin-top: 20px;
      margin-bottom: 20px;
      margin-top: 10px;
      margin-bottom: 20px;
      .item {
        clear: both;
      }
    }
    .total-info {
      margin-top: 20px;
      margin-bottom: 60px;
      .item {
        margin-bottom: 2px;
        .amount {
          font-weight: bold;
          font-size: 28px;
        }
      }
      .discount-detail {
        font-size: 12px;
        margin-left: 20px;
      }
    }
  }
}
</style>
