<template>
  <div class="userCoupon-container">
    <el-form :model="queryParams" class="search-form" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="ID" prop="id">
        <el-input
          v-model="queryParams.id"
          placeholder="请输入ID"
          clearable
          class="input-item"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="卡券ID" prop="couponId">
        <el-input
          v-model="queryParams.couponId"
          placeholder="请输入卡券ID"
          clearable
          class="input-item"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会员ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入会员ID"
          clearable
          class="input-item"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="会员号" prop="userNo">
        <el-input
          v-model="queryParams.userNo"
          placeholder="请输入会员号"
          clearable
          class="input-item"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input
          v-model="queryParams.mobile"
          placeholder="请输入会员手机号"
          clearable
          class="input-item"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="核销码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入核销二维码"
          clearable
          class="input-item"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select
          v-model="queryParams.type"
          clearable
          placeholder="卡券类型"
          class="input-item"
        >
          <el-option v-for="typeInfo in typeList" :key="typeInfo.key" :label="typeInfo.name" :value="typeInfo.key"/>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="状态"
          class="input-item"
          clearable
        >
          <el-option v-for="statusInfo in statusList" :key="statusInfo.key" :label="statusInfo.name" :value="statusInfo.key"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" class="main-button-middle" icon="el-icon-search" @click="handleQuery(false)" style="background: #00afff;border: 1px solid #00afff;">搜索</el-button>
        <el-button icon="el-icon-refresh" class="main-button-middle main-button-reset" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div class="table-container">
      <el-table ref="tables" v-loading="loading" :data="list" border style="width: 100%;" height="calc(100% - 10px)" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
        <el-table-column label="ID" prop="id" width="100"/>
        <el-table-column label="核销码" prop="code" width="150"/>
        <el-table-column label="会员ID" align="center" prop="userInfo.id">
          <template slot-scope="scope">
            <span v-if="scope.row.userInfo">
                <span>{{ scope.row.userInfo.id }}</span>
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="会员号" align="center" width="130" prop="userInfo.userNo">
          <template slot-scope="scope">
            <span v-if="scope.row.userInfo && scope.row.userInfo.userNo">{{ scope.row.userInfo.userNo }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="手机号" align="center" width="120" prop="userInfo.mobile">
          <template slot-scope="scope">
            <span v-if="scope.row.userInfo">{{ scope.row.userInfo.mobile ? scope.row.userInfo.mobile : '-' }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="会员名称" align="center" prop="userInfo.name">
          <template slot-scope="scope">
            <span v-if="scope.row.userInfo">
                <span>{{ scope.row.userInfo.name }}</span>
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="卡券类型" align="center" prop="type">
          <template slot-scope="scope">
            <span v-if="scope.row.type">{{ getName(typeList, scope.row.type) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="卡券名称" align="center" prop="name" width="120"/>
        <el-table-column label="所属店铺" align="center" prop="storeInfo" width="110">
          <template slot-scope="scope">
            <span v-if="scope.row.storeInfo">{{ scope.row.storeInfo.name }}</span>
            <span v-else>暂无</span>
          </template>
        </el-table-column>
        <el-table-column label="总面额 / 总次数" align="center" prop="amount" width="110">
           <template slot-scope="scope">
             <span v-if="scope.row.type != 'T'" style="color:red">{{ scope.row.amount }}元</span>
             <span v-else style="color:red"> {{ scope.row.amount }}次 </span>
           </template>
        </el-table-column>
        <el-table-column label="余额 / 核销次数" align="center" prop="balance" width="110">
          <template slot-scope="scope">
            <span v-if="scope.row.type != 'T'" style="color:forestgreen">{{ scope.row.balance }}元</span>
            <span v-else style="color:forestgreen"> {{ scope.row.balance }}次 </span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.status === 'A'">{{ getName(statusList, scope.row.status) }}</el-tag>
            <el-tag type="danger" v-else>{{ getName(statusList, scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="领取时间" align="center" width="160" prop="createTime">
          <template slot-scope="scope">
            <span v-if="scope.row.createTime">{{ parseTime(scope.row.createTime) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" fixed='right' width="140">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              class="main-text"
              v-hasPermi="['cashier:index']"
              @click="handleDetail(scope.row)"
              style="color: #00afff;"
            >详情</el-button>
            <el-button
              :disabled="scope.row.status != 'A'"
              size="mini"
              type="text"
              class="main-text"
              v-hasPermi="['cashier:index']"
              @click="handleConfirm(scope.row)"
              style="color: #00afff;"
            >核销</el-button>
            <el-button
              size="mini"
              type="text"
              class="main-text"
              v-hasPermi="['cashier:index']"
              @click="handleDelete(scope.row)"
              style="color: red;"
            >作废</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <pagination
      v-show="total>0"
      :total="total"
      class="pagination"
      :page.sync="queryParams.page"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!--详情对话框 start-->
    <el-dialog title="详情" :visible.sync="openDetail" class="common-dialog" width="700px" append-to-body>
      <el-form ref="detailForm" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="卡券类型：">
              <span class="coupon-type">[{{ getName(typeList, couponInfo.type) }}]</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="卡券名称：">
              <el-input style="width: 420px;" v-model="couponInfo.name" disabled maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="couponInfo.userInfo">
          <el-col :span="24">
            <el-form-item label="会员名称：">
              <el-input style="width: 420px;" v-model="couponInfo.userInfo.name" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="有效期：">
              <el-input style="width: 420px;" v-model="couponInfo.effectiveDate" disabled maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="couponInfo.amount">
          <el-col :span="24">
            <el-form-item label="卡券面额：">
              <el-input style="width: 200px;" :value="couponInfo.amount ? couponInfo.amount.toFixed(2) : '0.00'" disabled></el-input>
              <span class="unit">元</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="couponInfo.type=='P'">
          <el-col :span="24">
            <el-form-item label="卡券余额：" prop="balance">
              <el-input :value="couponInfo.balance ? couponInfo.balance.toFixed(2) : '0.00'" style="width: 200px;" disabled></el-input>
              <span class="unit">元</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="couponInfo.type=='T'">
          <el-col :span="24">
            <el-form-item label="核销次数：" prop="useRule">
              <span class="info">总共<b style="color:green;">{{ couponInfo.useRule }}</b>次，已核销<b style="color:red;"> {{ couponInfo.confirmCount }}</b>次</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="卡券说明：">
              <el-input v-model="couponInfo.description" type="textarea" rows="3" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="openDetail = false" class="main-button-reset">关 闭</el-button>
      </div>
    </el-dialog>
    <!--详情对话框 end-->
  </div>
</template>

<script>
import { getUserCouponList, deleteUserCoupon } from "@/api/userCoupon";
import { getConfirmInfo } from "@/api/coupon";
import { getName } from "@/utils/fuint";
import { Message } from "element-ui";
export default {
  name: "UserCouponIndex",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 详情对话框
      openDetail: false,
      // 卡券详情
      couponInfo: {},
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 卡券类型
      typeList: [],
      // 状态列表
      statusList: [],
      // 默认排序
      defaultSort: {prop: 'createTime', order: 'descending'},
      // 查询参数
      queryParams: {
        page: 1,
        pageSize: 10,
        mobile: '',
        userId: '',
        userNo: '',
        status: '',
        id: '',
        couponId: '',
        type: ''
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    getName,
    // 查询列表
    getList() {
      this.loading = true;
      getUserCouponList(this.queryParams).then(response => {
          this.list = response.data.paginationResponse.content;
          this.total = response.data.paginationResponse.totalElements;
          this.typeList = response.data.typeList;
          this.statusList = response.data.statusList;
          this.loading = false;
        }
      );
    },
    // 搜索按钮操作
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    // 重置按钮操作
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    // 排序触发事件
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    // 删除按钮操作
    handleDelete(row) {
      this.$confirm('是否确认作废ID等于' + row.id + '的数据项？').then(function() {
        return deleteUserCoupon(row.id);
      }).then(() => {
        this.getList();
        Message({
          message: "提示：作废成功",
          type: "success"
        });
      }).catch(() => {});
    },
    // 核销按钮操作
    handleConfirm(row) {
      this.$emit('doConfirmCoupon', row.code);
    },
    // 查看详情操作
    handleDetail(row) {
      const app = this;
      app.loading = true;
      getConfirmInfo({ id: row.id, code: row.code }).then(response => {
        app.couponInfo = response.data.couponInfo;
        app.couponInfo.userInfo = response.data.userInfo;
        app.openDetail = true;
        app.loading = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  width: 12px;
  height: 12px;
  display: block;
}
::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #bfbfc0;
  border-radius: 5px;
}
::v-deep .el-table--scrollable-y .el-table__body-wrapper {
  overflow: overlay !important;
}
::v-deep .el-table {
  flex: 1;
  overflow: auto;
}
.userCoupon-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: calc(100% - 80px);
  margin-left: 80px;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
  padding-bottom: 60px; /* 为固定分页栏留出空间 */

  .search-form {
    border: solid 1px #cccccc;
    margin-top: 0px;
    padding: 15px 10px 0px 10px;
    background: #ffffff;
    margin-bottom: 5px;
    border-radius: 5px;
  }

  .table-container {
    flex: 1;
    overflow: hidden;
    margin-bottom: -20px;
  }

  .pagination {
    position: fixed;
    bottom: 0;
    left: 90px;
    right: 0;
    height: 40px;
    line-height: 40px;
    display: flex;
    justify-content: center;
    background: #ffffff;
    color: #333333;
    border-top: 1px solid #e6e6e6;
    z-index: 999;
    width: calc(100% - 98px);
    padding: 0 10px;
    box-sizing: border-box;
    margin-top: -10px;
    margin-bottom: 5px;
  }

  .input-item {
    width: 150px;
    margin-top: 0px;
  }
}

.coupon-type {
  font-weight: bold;
  color: #ff5b57;
}

.unit {
  margin-left: 5px;
}

.main-text {
  color: #00afff;
}
</style>
<style scoped>
::v-deep .el-pagination.is-background span {
  color: #333;
}
::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {
  background-color: #409EFF;
  color: #fff;
}
::v-deep .el-pagination.is-background .el-pager li {
  background-color: #f4f4f5;
  color: #606266;
}
::v-deep .el-pagination button {
  color: #606266;
}
::v-deep .el-pagination .btn-prev,
::v-deep .el-pagination .btn-next {
  background-color: #f4f4f5;
}
::v-deep .el-select .el-input .el-input__inner {
  color: #606266;
}
::v-deep .el-pagination__jump {
  color: #606266;
}
::v-deep .el-pagination__editor.el-input .el-input__inner {
  color: #606266;
}
</style>

