<!--<template>-->
<!--  <div class="coupon-container">-->
<!--    <el-form :model="queryParams" class="search-form" ref="form" size="small" :inline="true" label-width="68px">-->
<!--      <el-form-item label="核销码" prop="code">-->
<!--        <el-input-->
<!--          class="input-item"-->
<!--          v-model="couponCode"-->
<!--          placeholder="请使用扫码枪扫描卡券二维码，或手工输入核销码..."-->
<!--          clearable-->
<!--          @keyup.enter.native="submitConfirm"-->
<!--        />-->
<!--      </el-form-item>-->
<!--      <el-form-item>-->
<!--        <el-button type="primary" class="main-button-middle" icon="el-icon-check" @click="submitConfirm" style="background: #00afff;border: 1px solid #00afff;">确定核销</el-button>-->
<!--        <el-button icon="el-icon-back" class="main-button-middle main-button-reset" @click="backUserCoupon">返回列表</el-button>-->
<!--      </el-form-item>-->
<!--    </el-form>-->
<!--    -->
<!--    &lt;!&ndash; 提示信息区域 &ndash;&gt;-->
<!--    <div class="table-container" v-if="!couponInfo.id">-->
<!--      <div class="empty-tip">-->
<!--        <i class="el-icon-tickets"></i>-->
<!--        <p>请输入卡券核销码后点击"确定核销"按钮</p>-->
<!--      </div>-->
<!--    </div>-->

<!--    &lt;!&ndash; 卡券信息展示区域 &ndash;&gt;-->
<!--    <div class="table-container" v-if="couponInfo.id">-->
<!--      <el-table :data="[couponInfo]" border style="width: 100%;" height="calc(100% - 10px)">-->
<!--        <el-table-column label="卡券ID" prop="id" width="80" align="center"/>-->
<!--        <el-table-column label="卡券类型" prop="type" align="center">-->
<!--          <template slot-scope="scope">-->
<!--            <span class="coupon-type">{{ getName(typeList, scope.row.type) }}</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column label="卡券名称" prop="name" align="center"/>-->
<!--        <el-table-column label="有效期" prop="effectiveDate" align="center"/>-->
<!--        <el-table-column label="金额/次数" align="center">-->
<!--          <template slot-scope="scope">-->
<!--            <span v-if="scope.row.amount">{{ scope.row.amount.toFixed(2) }} 元</span>-->
<!--            <span v-else-if="scope.row.type=='T'">{{ scope.row.useRule }} 次</span>-->
<!--            <span v-else>-</span>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column label="状态" align="center">-->
<!--          <template slot-scope="scope">-->
<!--            <el-tag type="success" v-if="scope.row.status == 'A'">正常</el-tag>-->
<!--            <el-tag type="danger" v-else>已使用</el-tag>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column label="操作" width="150" align="center">-->
<!--          <template slot-scope="scope">-->
<!--            <el-button-->
<!--              size="mini"-->
<!--              type="text"-->
<!--              icon="el-icon-check"-->
<!--              class="main-text"-->
<!--              style="color: #00afff;"-->
<!--              @click="open = true"-->
<!--            >核销</el-button>-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--      </el-table>-->
<!--    </div>-->

<!--    &lt;!&ndash;核销对话框 start&ndash;&gt;-->
<!--    <el-dialog title="核销卡券" :visible.sync="open" class="common-dialog" width="700px" append-to-body>-->
<!--      <el-form ref="confirmForm" :model="confirmForm" :rules="confirmRules" label-width="120px">-->
<!--        <el-row>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="卡券类型：">-->
<!--              <span class="coupon-type">{{ getName(typeList, couponInfo.type) }}</span>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-row>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="卡券名称：">-->
<!--              <el-input style="width: 420px;" v-model="couponInfo.name" disabled maxlength="100" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-row v-if="userInfo">-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="会员名称：">-->
<!--              <el-input style="width: 420px;" v-model="userInfo.name" disabled></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-row>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="有效期：">-->
<!--              <el-input style="width: 420px;" v-model="couponInfo.effectiveDate" disabled maxlength="100" />-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-row v-if="couponInfo.amount">-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="卡券面额：">-->
<!--              <el-input style="width: 200px;" :value="couponInfo.amount ? couponInfo.amount.toFixed(2) : '0.00'" disabled></el-input>-->
<!--              <span class="unit">元</span>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-row v-if="couponInfo.type=='P'">-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="卡券余额：" prop="balance">-->
<!--              <el-input :value="couponInfo.balance ? couponInfo.balance.toFixed(2) : '0.00'" style="width: 200px;" disabled></el-input>-->
<!--              <span class="unit">元</span>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-row v-if="couponInfo.type=='P'">-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="核销金额：" prop="amount">-->
<!--              <el-input v-model="confirmForm.amount" style="width: 200px;" placeholder="请输入核销金额"></el-input>-->
<!--              <span class="unit">元</span>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->

<!--        <el-row v-if="couponInfo.type=='T'">-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="核销次数：" prop="useRule">-->
<!--              <span class="info">总共<b style="color:green;">{{ couponInfo.useRule }}</b>次，已核销<b style="color:red;"> {{ couponInfo.confirmCount }}</b>次</span>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->

<!--        <el-row>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="核销备注：">-->
<!--              <el-input v-model="confirmForm.remark" type="textarea" rows="2" placeholder="请输入核销备注"></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--        <el-row>-->
<!--          <el-col :span="24">-->
<!--            <el-form-item label="卡券说明：">-->
<!--              <el-input v-model="couponInfo.description" type="textarea" rows="3" disabled></el-input>-->
<!--            </el-form-item>-->
<!--          </el-col>-->
<!--        </el-row>-->
<!--      </el-form>-->
<!--      <div slot="footer" class="dialog-footer">-->
<!--        <el-button type="primary" class="main-button" @click="doSubmitConfirm" style="background: #00afff;border: 1px solid #00afff;">确定核销</el-button>-->
<!--        <el-button class="main-button-reset" @click="cancelConfirm">取 消</el-button>-->
<!--      </div>-->
<!--    </el-dialog>-->
<!--    &lt;!&ndash;核销对话框 end&ndash;&gt;-->
<!--  </div>-->
<!--</template>-->

<!--<script>-->
<!--import { getConfirmInfo, doConfirm } from "@/api/coupon";-->
<!--import { getName } from "@/utils/fuint";-->
<!--import { Message } from "element-ui";-->
<!--export default {-->
<!--  props: {-->
<!--    couponCode: {-->
<!--      type:[String],-->
<!--      default:()=>""-->
<!--    }-->
<!--  },-->
<!--  data() {-->
<!--    return {-->
<!--      // 遮罩层-->
<!--      loading: false,-->
<!--      // 核销弹框-->
<!--      open: false,-->
<!--      // 查询参数-->
<!--      queryParams: {-->
<!--        code: ""-->
<!--      },-->
<!--      confirmForm: { userCouponId: '', amount: '', remark: '' },-->
<!--      couponInfo: {},-->
<!--      userInfo: {},-->
<!--      typeList: [],-->
<!--      confirmRules: {-->
<!--        amount: [-->
<!--          { required: true, message: "核销金额不能为空", trigger: "blur" },-->
<!--        ]-->
<!--      }-->
<!--    };-->
<!--  },-->
<!--  methods: {-->
<!--    getName,-->
<!--    // 取消按钮-->
<!--    cancel() {-->
<!--      return false;-->
<!--    },-->
<!--    // 确定核销-->
<!--    submitConfirm: function() {-->
<!--      if (!this.couponCode) {-->
<!--        Message({-->
<!--          message: "请输入核销码",-->
<!--          type: "warning"-->
<!--        });-->
<!--        return;-->
<!--      }-->
<!--      this.loading = true;-->
<!--      getConfirmInfo({ code: this.couponCode }).then(response => {-->
<!--         this.couponInfo = response.data.couponInfo;-->
<!--         this.confirmForm.userCouponId = this.couponInfo.id-->
<!--         this.userInfo = response.data.userInfo ? response.data.userInfo : null;-->
<!--         this.typeList = response.data.typeList;-->
<!--         this.loading = false;-->
<!--      }).catch(() => {-->
<!--         this.loading = false;-->
<!--      });-->
<!--    },-->
<!--    // 返回列表-->
<!--    backUserCoupon: function() {-->
<!--      this.$emit('doUserCoupon');-->
<!--    },-->
<!--    // 执行核销-->
<!--    doSubmitConfirm: function() {-->
<!--      this.$refs["confirmForm"].validate(valid => {-->
<!--        if (valid) {-->
<!--            doConfirm(this.confirmForm).then(response => {-->
<!--              if (response.data) {-->
<!--                this.open = false-->
<!--                Message({-->
<!--                  message: "卡券核销成功",-->
<!--                  type: "success"-->
<!--                });-->
<!--                this.confirmForm.remark = ''-->
<!--                // 核销成功后重置页面-->
<!--                this.couponInfo = {};-->
<!--              }-->
<!--            }).catch(() => {-->
<!--               // empty-->
<!--            });-->
<!--        }-->
<!--      });-->
<!--    },-->
<!--    cancelConfirm: function() {-->
<!--       this.open = false-->
<!--    }-->
<!--  }-->
<!--};-->
<!--</script>-->
<!--<style lang="scss" scoped>-->
<!--::v-deep .el-table__body-wrapper::-webkit-scrollbar {-->
<!--  width: 12px;-->
<!--  height: 12px;-->
<!--  display: block;-->
<!--}-->
<!--::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {-->
<!--  background-color: #bfbfc0;-->
<!--  border-radius: 5px;-->
<!--}-->
<!--::v-deep .el-table&#45;&#45;scrollable-y .el-table__body-wrapper {-->
<!--  overflow: overlay !important;-->
<!--}-->
<!--::v-deep .el-table {-->
<!--  flex: 1;-->
<!--  overflow: auto;-->
<!--}-->
<!--.coupon-container {-->
<!--  display: flex;-->
<!--  flex-direction: column;-->
<!--  height: 100%;-->
<!--  width: calc(100% - 80px);-->
<!--  margin-left: 80px;-->
<!--  padding: 10px;-->
<!--  box-sizing: border-box;-->
<!--  position: relative;-->
<!--  padding-bottom: 60px; /* 为固定分页栏留出空间 */-->

<!--  .search-form {-->
<!--    border: solid 1px #cccccc;-->
<!--    margin-top: 0px;-->
<!--    padding: 15px 10px 0px 10px;-->
<!--    background: #ffffff;-->
<!--    margin-bottom: 5px;-->
<!--    border-radius: 5px;-->
<!--  }-->

<!--  .table-container {-->
<!--    flex: 1;-->
<!--    overflow: hidden;-->
<!--    margin-bottom: -20px;-->
<!--    background: #ffffff;-->
<!--    border: solid 1px #cccccc;-->
<!--    border-radius: 5px;-->
<!--    padding: 10px;-->

<!--    .empty-tip {-->
<!--      display: flex;-->
<!--      flex-direction: column;-->
<!--      align-items: center;-->
<!--      justify-content: center;-->
<!--      height: 200px;-->
<!--      color: #909399;-->

<!--      i {-->
<!--        font-size: 50px;-->
<!--        margin-bottom: 20px;-->
<!--      }-->

<!--      p {-->
<!--        font-size: 16px;-->
<!--      }-->
<!--    }-->
<!--  }-->

<!--  .input-item {-->
<!--    width: 420px;-->
<!--  }-->
<!--}-->

<!--.input-item {-->
<!--  ::v-deep .el-input__inner {-->
<!--    line-height: 36px;-->
<!--    height: 36px;-->
<!--  }-->
<!--}-->

<!--.form-item {-->
<!--  ::v-deep .el-form-item__label {-->
<!--    line-height: 36px;-->
<!--    height: 36px;-->
<!--  }-->
<!--}-->

<!--.coupon-type {-->
<!--  font-weight: bold;-->
<!--  color: #ff5b57;-->
<!--}-->

<!--.unit {-->
<!--  margin-left: 5px;-->
<!--}-->

<!--::v-deep .el-table .cell {-->
<!--  text-align: center;-->
<!--}-->

<!--.main-text {-->
<!--  color: #00afff;-->
<!--}-->
<!--</style>-->
<!--<style scoped>-->
<!--::v-deep .el-pagination.is-background span {-->
<!--  color: #333;-->
<!--}-->
<!--::v-deep .el-pagination.is-background .el-pager li:not(.disabled).active {-->
<!--  background-color: #409EFF;-->
<!--  color: #fff;-->
<!--}-->
<!--::v-deep .el-pagination.is-background .el-pager li {-->
<!--  background-color: #f4f4f5;-->
<!--  color: #606266;-->
<!--}-->
<!--::v-deep .el-pagination button {-->
<!--  color: #606266;-->
<!--}-->
<!--::v-deep .el-pagination .btn-prev,-->
<!--::v-deep .el-pagination .btn-next {-->
<!--  background-color: #f4f4f5;-->
<!--}-->
<!--::v-deep .el-select .el-input .el-input__inner {-->
<!--  color: #606266;-->
<!--}-->
<!--::v-deep .el-pagination__jump {-->
<!--  color: #606266;-->
<!--}-->
<!--::v-deep .el-pagination__editor.el-input .el-input__inner {-->
<!--  color: #606266;-->
<!--}-->
<!--</style>-->


<template>
  <div class="coupon-container">
    <div class="main-panel">
      <el-form class="form" ref="form" label-width="80px">
        <el-row>
          <el-col :span="24">
            <el-form-item class="form-item" label="核销码" prop="code">
              <el-input class="input-item" v-model="couponCode" placeholder="请使用扫码枪扫描卡券二维码，或手工输入核销码..." maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="action">
        <el-button type="primary" class="confirm-button" @click="submitConfirm">确定核销</el-button>
        <el-button class="back-button" @click="backUserCoupon">返回列表</el-button>
      </div>
    </div>

    <!--核销对话框 start-->
    <el-dialog title="核销卡券" :visible.sync="open" class="common-dialog" width="700px" append-to-body>
      <el-form ref="confirmForm" :model="confirmForm" :rules="confirmRules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="卡券类型：">
              <span class="coupon-type">{{ getName(typeList, couponInfo.type) }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="卡券名称：">
              <el-input style="width: 420px;" v-model="couponInfo.name" disabled maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="userInfo">
          <el-col :span="24">
            <el-form-item label="会员名称：">
              <el-input style="width: 420px;" v-model="userInfo.name" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="有效期：">
              <el-input style="width: 420px;" v-model="couponInfo.effectiveDate" disabled maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="couponInfo.amount">
          <el-col :span="24">
            <el-form-item label="卡券面额：">
              <el-input style="width: 200px;" v-model="couponInfo.amount.toFixed(2)" disabled></el-input>
              <span class="unit">元</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="couponInfo.type=='P'">
          <el-col :span="24">
            <el-form-item label="卡券余额：" prop="balance">
              <el-input v-model="couponInfo.balance.toFixed(2)" style="width: 200px;" disabled></el-input>
              <span class="unit">元</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="couponInfo.type=='P'">
          <el-col :span="24">
            <el-form-item label="核销金额：" prop="amount">
              <el-input v-model="confirmForm.amount" style="width: 200px;" placeholder="请输入核销金额"></el-input>
              <span class="unit">元</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="couponInfo.type=='T'">
          <el-col :span="24">
            <el-form-item label="核销次数：" prop="useRule">
              <span class="info">总共<b style="color:green;">{{ couponInfo.useRule }}</b>次，已核销<b style="color:red;"> {{ couponInfo.confirmCount }}</b>次</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <el-form-item label="核销备注：">
              <el-input v-model="confirmForm.remark" type="textarea" rows="2" placeholder="请输入核销备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="卡券说明：">
              <el-input v-model="couponInfo.description" type="textarea" rows="3" disabled></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" class="main-button" @click="doSubmitConfirm">确定核销</el-button>
        <el-button class="main-button main-button-reset" @click="cancelConfirm">取 消</el-button>
      </div>
    </el-dialog>
    <!--核销对话框 end-->
  </div>
</template>

<script>
import { getConfirmInfo, doConfirm } from "@/api/coupon";
import { Message } from "element-ui";
export default {
  props: {
    couponCode: {
      type:[String],
      default:()=>""
    }
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 核销弹框
      open: false,
      confirmForm: { userCouponId: '', amount: '', remark: '' },
      couponInfo: {},
      userInfo: {},
      typeList: [],
      confirmRules: {
        amount: [
          { required: true, message: "核销金额不能为空", trigger: "blur" },
        ]
      }
    };
  },
  methods: {
    // 取消按钮
    cancel() {
      return false;
    },
    // 确定核销
    submitConfirm: function() {
      getConfirmInfo({ code: this.couponCode }).then(response => {
        this.couponInfo = response.data.couponInfo;
        this.confirmForm.userCouponId = this.couponInfo.id
        this.userInfo = response.data.userInfo ? response.data.userInfo : null;
        this.typeList = response.data.typeList;
        this.open = true
      }).catch(() => {
        // empty
      });
    },
    // 返回列表
    backUserCoupon: function() {
      this.$emit('doUserCoupon');
    },
    // 执行核销
    doSubmitConfirm: function() {
      this.$refs["confirmForm"].validate(valid => {
        if (valid) {
          doConfirm(this.confirmForm).then(response => {
            if (response.data) {
              this.open = false
              Message({
                message: "卡券核销成功",
                type: "success"
              });
              this.confirmForm.remark = ''
            }
          }).catch(() => {
            // empty
          });
        }
      });
    },
    cancelConfirm: function() {
      this.open = false
    }
  }
};
</script>
<style scoped>
.coupon-container {
  position: absolute;
  top: 30px;
  left: 165px;
  right: 10px;
}
.main-panel {
  margin-top: 20px;
  display: flex;
  border: solid 1px #cccccc;
  padding: 60px 0px 40px 0px;
  border-radius: 2px;
  width: 100%;
  background: #f5f5f5;
}

.main-panel .action {
  margin-left: 10px;
  min-width: 200px;
}

.main-panel .el-input {
  min-width: 580px;
}

.input-item >>> .el-input__inner {
  border: #00acac solid 2px;
  line-height: 50px;
  height: 50px;
}

.form-item >>> .el-form-item__label {
  line-height: 50px;
  height: 50px;
}

.coupon-type {
  font-weight: bold;
  color: #ff5b57;
}

.unit {
  margin-left: 5px;
}
.confirm-button {
  background: #00acac;
  line-height: 50px;
  padding: 0px 25px 0px 25px;
  height: 50px;
}
.back-button {
  line-height: 50px;
  padding: 0px 25px 0px 25px;
  height: 50px;
}
</style>
