<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>支付宝</title>
</head>

<body>
    <div id="alipay"></div>
</body>
<script>
    const { ipc<PERSON><PERSON>er } = require('electron')
    window.onload = () => {
      ipcRenderer.on('send-data', (event, arg) => {
        document.getElementById('alipay').innerHTML = arg
        document.forms.alipay_submit.submit()
      })
    }
</script>

</html>