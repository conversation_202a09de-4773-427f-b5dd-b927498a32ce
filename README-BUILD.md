# Electron应用构建指南

## 问题描述
在Windows系统上构建Electron应用时遇到权限问题，无法创建符号链接。

## 解决方案

### 方案1：使用管理员权限构建（推荐）

1. **双击运行 `build-as-admin.bat`**
   - 这个文件会自动以管理员权限启动构建过程

2. **或者手动以管理员身份运行：**
   ```bash
   # 以管理员身份打开PowerShell
   # 导航到项目目录
   cd D:\QCNH\fuint\fuintCashier
   
   # 清理缓存
   Remove-Item -Path "$env:LOCALAPPDATA\electron-builder\Cache" -Recurse -Force -ErrorAction SilentlyContinue
   
   # 设置环境变量
   $env:CSC_IDENTITY_AUTO_DISCOVERY = "false"
   
   # 运行构建
   npm run build:dir
   ```

### 方案2：启用开发者模式（Windows 10/11）

1. 打开 **设置** > **更新和安全** > **开发者选项**
2. 启用 **开发者模式**
3. 重启计算机
4. 重新运行构建命令

### 方案3：修改组策略（高级用户）

1. 按 `Win + R`，输入 `gpedit.msc`
2. 导航到：计算机配置 > Windows设置 > 安全设置 > 本地策略 > 用户权限分配
3. 找到 "创建符号链接" 策略
4. 添加当前用户到此策略
5. 重启计算机

## 可用的构建命令

```bash
# 构建目录版本（不打包）
npm run build:dir

# 构建便携式版本
npm run build:portable

# 构建安装包（推荐用于分享）
npm run build:installer

# 构建Windows安装包
npm run build:win64

# 构建所有平台
npm run build
```

## 构建输出

成功构建后，文件将输出到 `build/` 目录：
- `build/win-unpacked/` - 未打包的应用程序文件夹
- `build/68U选 Setup 1.0.0.exe` - 安装包（用于分享）

## 🚀 如何分享给其他用户

### 方案1：分享安装包（推荐）

1. **创建安装包**：
   ```bash
   # 双击运行 build-installer.bat
   # 或以管理员身份运行：
   npm run build:installer
   ```

2. **分享安装包**：
   - 找到 `build/68U选 Setup 1.0.0.exe` 文件
   - 将这个文件发送给其他用户
   - 用户双击安装即可使用

### 方案2：分享便携版

1. **压缩应用文件夹**：
   - 找到 `build/win-unpacked/` 文件夹
   - 压缩整个文件夹为 `.zip` 或 `.rar` 文件
   - 分享压缩包

2. **用户使用方法**：
   - 解压到任意目录
   - 运行 `68U选.exe` 文件

### ❌ 错误的分享方法

**不要只分享单独的 .exe 文件！**
- Electron应用需要完整的文件夹结构
- 缺少依赖文件会导致"找不到 ffmpeg.dll"等错误

## 故障排除

如果构建仍然失败：

1. **清理所有缓存：**
   ```bash
   npm run build:clean
   Remove-Item -Path "$env:LOCALAPPDATA\electron-builder\Cache" -Recurse -Force
   Remove-Item -Path "node_modules" -Recurse -Force
   npm install
   ```

2. **检查Node.js版本：**
   ```bash
   node --version
   npm --version
   ```
   建议使用Node.js 16.x或18.x版本

3. **使用yarn代替npm：**
   ```bash
   yarn install
   yarn build:dir
   ```

## 注意事项

- 构建过程可能需要几分钟时间
- 确保有足够的磁盘空间（至少2GB）
- 如果使用杀毒软件，可能需要将项目目录添加到白名单
- 网络连接需要稳定，因为需要下载依赖文件
